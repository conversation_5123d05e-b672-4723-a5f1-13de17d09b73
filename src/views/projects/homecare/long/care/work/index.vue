<template>
    <form-tree-list :tree-width="220" :class="{ 'ut-fullscreen': fullscreen }">
        <template #tree>
            <tree :height="height" @node-click="nodeClick" />
        </template>
        <template #list>
            <div class="table-container">
                <form-list
                    :loading="listLoading"
                    :columns="columns"
                    :height="height"
                    :op-width="90"
                    :op-fixed="true"
                    :data-list="dataList"
                    :select-window="selectWindow"
                    :select-single="selectSingle"
                    :show-list="showList"
                    table-name="long-care-work"
                    :show-checkbox="false"
                    @select="handleSelect"
                    @fetchData="fetchData"
                    @fullscreen="onFullscreen"
                    @selectRows="onSelectRows"
                >
                    <template #button>
                        <el-button icon="el-icon-data-line" @click="queryFormVisible = true">筛选</el-button>
                        <el-button v-if="isFiltered" icon="el-icon-files" type="primary" @click="clearFilter">全部</el-button>
                        <!-- <el-button icon="el-icon-plus" type="primary" @click="handleAdd()">新增</el-button> -->
                    </template>
                    <template #op="{ row }">
                        <!-- <el-button type="text" @click="handleEdit(row)">编辑</el-button> -->
                        <el-button type="text" @click="handleInfo(row)">详细</el-button>
                        <!-- <el-button v-if="!row.isAudit && !row.isSubmit" type="text" @click="handleSubmit(row)">提交</el-button>
                        <el-button v-if="!row.isAudit && row.isSubmit" type="text" @click="handleSubmitCancel(row)">撤销提交</el-button> -->
                    </template>
                    <template #cell="{ row, item }">
                        <div v-if="item.prop === 'workDate'">
                            {{ row[item.prop] }}
                            <el-tag v-if="row.isJudan" type="danger" size="mini">拒单</el-tag>
                        </div>
                        <div v-else-if="item.prop === 'color'" class="color-box">
                            <span class="color-preview" :style="{ backgroundColor: row.color }"></span>
                            {{ row[item.prop] }}
                        </div>
                        <div v-else-if="item.prop === 'name' && row['_customer']">
                            {{ row['_customer'].name }}
                        </div>
                        <div v-else-if="item.prop === 'sex'">
                            <span v-if="row.sex == 1">男</span>
                            <span v-if="row.sex == 2">女</span>
                        </div>
                        <!-- <div v-else-if="item.prop === 'phone' && row['_customer']">
                            {{ row['_customer'].phone }}
                        </div>
                        <div v-else-if="item.prop === 'idcard' && row['_customer']">
                            {{ row['_customer'].idcard }}
                        </div>
                        <div v-else-if="item.prop === 'address' && row['_customer']">
                            {{ row['_customer'].address }}
                        </div> -->
                        <div v-else-if="item.prop === 'projectCount'">
                            <span v-if="row.projectCount > 0">{{ row.projectCount }}</span>
                            <span v-if="row.projectCount <= 0"></span>
                        </div>
                        <div v-else-if="item.prop === 'totalDuration'">
                            <span v-if="row.totalDuration > 0">{{ row.totalDuration }}/{{ row.schedulingDuration }}</span>
                            <span v-if="row.totalDuration <= 0">计划:{{ row.schedulingDuration }}分钟</span>
                        </div>
                        <div v-else-if="item.prop === 'isSubmit'">
                            <span v-if="row.isSubmit">已提交</span>
                        </div>
                        <div v-else-if="item.prop === 'isAudit'">
                            <span v-if="row.isAudit">已审核</span>
                        </div>
                        <div v-else-if="item.prop === 'uploadData'">
                            <span v-if="row.uploadData">已上传</span>
                        </div>
                        <div v-else-if="item.prop === 'schedulingBegin'">
                            <!-- <span >{{row.workDate}}</span> -->
                            <span>{{ row.schedulingBegin }}-{{ row.schedulingEnd }}</span>
                        </div>

                        <span v-else>{{ row[item.prop] }}</span>
                    </template>
                </form-list>

                <!-- 详细 -->
                <ut-modal v-model="infoFormVisible" title="护理记录详细" width="800px">
                    <info :parent-data="selectRow" @close="infoFormVisible = false" />
                </ut-modal>

                <!-- 筛选 -->
                <ut-modal v-model="queryFormVisible" title="筛选条件" width="400px">
                    <query :init-data="queryData" @close="queryFormVisible = false" @query="queryHandle" />
                </ut-modal>
            </div>
        </template>
    </form-tree-list>
</template>

<script>
import { mapGetters } from 'vuex'
import Vue from 'vue'
import FormTreeList from '@/views/components/form-tree-list'
import FormList from '@/views/components/form-list'
import Tree from './tree.vue'
import Info from './info.vue'
import Query from './query.vue'

export default {
    name: 'Works',
    components: {
        FormTreeList,
        FormList,
        Tree,
        Info,
        Query,
    },
    props: {
        height: {
            type: Number,
            default: () => Vue.prototype.$baseTableHeight(1),
        },
        selectWindow: {
            type: Boolean,
            default: false,
        },
        selectSingle: {
            type: Boolean,
            default: false,
        },
        showList: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            listLoading: false,
            fullscreen: false,
            treeNode: {},
            columns: [
                {
                    label: '序',
                    align: 'center',
                    prop: 'index',
                    width: '50',
                    fixed: 'left',
                    show: true,
                },
                {
                    label: '护理日期',
                    align: 'center',
                    prop: 'workDate',
                    width: '150',
                    fixed: 'left',
                    show: true,
                },
                {
                    label: '分组名称',
                    align: 'center',
                    prop: 'groupName',
                    width: '160',
                    fixed: 'left',
                    show: true,
                },
                {
                    label: '护理员',
                    align: 'center',
                    prop: 'attendantName',
                    width: '120',
                    fixed: 'left',
                    show: true,
                },
                {
                    label: '客户姓名',
                    align: 'left',
                    prop: 'name',
                    width: '120',
                    fixed: 'left',
                    show: true,
                },
                {
                    label: '性别',
                    align: 'center',
                    prop: 'sex',
                    width: '60',
                    show: true,
                },
                {
                    label: '电话',
                    align: 'center',
                    prop: 'phone',
                    width: '120',
                    show: true,
                },
                // {
                // 	label: '证件号码',
                // 	align: 'center',
                // 	prop: 'idcard',
                // 	minWidth:'180',
                // 	show: true,
                // },
                {
                    label: '地址',
                    align: 'left',
                    prop: 'address',
                    minWidth: '220',
                    show: true,
                },
                {
                    label: '计划时间',
                    align: 'center',
                    prop: 'schedulingBegin',
                    minWidth: '120',
                    show: true,
                },

                {
                    label: '签到时间',
                    align: 'center',
                    prop: 'checkInTime',
                    width: '160',
                    show: true,
                },
                {
                    label: '签退时间',
                    align: 'center',
                    prop: 'checkOutTime',
                    width: '160',
                    show: true,
                },
                {
                    label: '时长',
                    align: 'center',
                    prop: 'totalDuration',
                    width: '130',
                    show: true,
                },
                {
                    label: '项目数',
                    align: 'center',
                    prop: 'projectCount',
                    width: '90',
                    show: true,
                },
                {
                    label: '服务资料',
                    align: 'center',
                    prop: 'uploadData',
                    width: '90',
                    show: true,
                },
                {
                    label: '提交',
                    align: 'center',
                    prop: 'isSubmit',
                    width: '90',
                    show: true,
                },
                {
                    label: '审核',
                    align: 'center',
                    prop: 'isAudit',
                    width: '90',
                    show: true,
                },
            ],
            dataList: {
                info: [],
                page: 0,
                record: 0,
            },
            page: {
                key: '',
                pageindex: 1,
                pagesize: 20,
            },
            search: {
                beginDate: '',
                endDate: '',
                isJudan: '',
                // isAudit: '',
                // isSubmit: '',
            },
            selectRow: {},
            selectRows: [],
            infoFormVisible: false,
            queryFormVisible: false,
            isFiltered: false,
        }
    },
    computed: {
        ...mapGetters({
            comm: 'comm/comm',
        }),
        queryData() {
            return Object.assign({}, this.search)
        }
    },
    mounted() {},
    methods: {
        async fetchData(pageReq) {
            if (pageReq) this.page = pageReq
            if (!this.treeNode.id && !this.treeNode.isRoot) return
            this.listLoading = true
            const queryParams = {
                communityId: this.comm.id,
                ...this.search,
                page: this.page,
            }
            // 接口返回拼写有问题，兜底适配
            if (this.treeNode?.type === 'group' || this.treeNode?.type === 'gruop') {
                queryParams.groupId = this.treeNode.id
            } else if (this.treeNode?.type === 'attendant') {
                queryParams.groupId = this.treeNode.pId || this.treeNode.pid
                queryParams.attendantId = this.treeNode.id
            }
            if (this.search.beginDate) queryParams.beginDate = this.search.beginDate
            if (this.search.endDate) queryParams.endDate = this.search.endDate
            if (this.search.isJudan !== '') queryParams.isJudan = this.search.isJudan
            const { data } = await this.$ut.api('homecarelong/care/work/searchTjListpg', queryParams).finally(() => {
                this.listLoading = false
            })
            this.dataList = data
        },
        async getCustomerInfo({ info }) {
            let ids = info.filter((u) => u.customerId).map((u) => u.customerId)
            if (!ids || !ids.length) return
            let params = {
                ids: ids,
                communityId: this.comm.id,
                module: 'long',
            }
            try {
                const { data } = await this.$ut.api('homecarecustomer/info/listByIds', params)
                this.dataList.info.forEach((item) => {
                    let obj = data.find((u) => u.id === item.customerId)
                    if (obj) this.$set(item, '_customer', obj)
                })
            } finally {
                this.listLoading = false
            }
        },
        handleSelect(rows) {
            this.$emit('select', rows)
        },
        nodeClick(node) {
            this.treeNode = node
            this.page.pageindex = 1
            this.fetchData()
        },
        onFullscreen(v) {
            this.fullscreen = v
        },
        onSelectRows(rows) {
            this.selectRows = rows
        },
        queryHandle(query) {
            this.queryFormVisible = false
            this.search = Object.assign({}, query)
            this.isFiltered = !!(query.beginDate || query.endDate || query.isJudan !== '')
            this.page.pageindex = 1
            this.fetchData()
        },
        clearFilter() {
            this.search = {
                beginDate: '',
                endDate: '',
                isJudan: '',
            }
            this.isFiltered = false
            this.page.pageindex = 1
            this.fetchData()
        },
        handleInfo(row) {
            this.selectRow = row
            this.infoFormVisible = true
        },
        // handleSubmit(row) {
        //     this.$baseConfirm('确认提交吗？', '确认操作', async () => {
        //         this.$ut
        //             .api('homecarelong/care/work/submit', {
        //                 communityId: this.comm.id,
        //                 id: row.id,
        //             })
        //             .then(() => {
        //                 this.$baseMessage('操作成功！', 'success', 'ut-hey-message-success')
        //                 this.fetchData()
        //             })
        //     })
        // },
        // handleSubmitCancel(row) {
        //     this.$baseConfirm('确认撤销提交吗？', '确认操作', async () => {
        //         this.$ut
        //             .api('homecarelong/care/work/submitCancel', {
        //                 communityId: this.comm.id,
        //                 id: row.id,
        //             })
        //             .then(() => {
        //                 this.$baseMessage('操作成功！', 'success', 'ut-hey-message-success')
        //                 this.fetchData()
        //             })
        //     })
        // },
    },
}
</script>

<style lang="scss" scoped>
.color-box {
    display: flex;
    align-items: center;
    justify-content: center;

    .color-preview {
        width: 20px;
        height: 20px;
        margin-right: 10px;
        border-radius: 2px;
    }
}
</style>
