<template>
    <div>
        <el-form ref="form" class="ut-body ut-form" label-width="120px" :model="form" :rules="rules">
            <el-form-item label="开始日期：" prop="beginDate">
                <el-date-picker
                    v-model="form.beginDate"
                    type="datetime"
                    format="yyyy年MM月dd日 HH:mm:ss"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="请选择开始日期"
                    style="width: 100%"
                />
            </el-form-item>
            
            <el-form-item label="结束日期：" prop="endDate">
                <el-date-picker
                    v-model="form.endDate"
                    type="datetime"
                    format="yyyy年MM月dd日 HH:mm:ss"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="请选择结束日期"
                    style="width: 100%"
                />
            </el-form-item>
            
            <el-form-item label="是否拒单：" prop="isJudan">
                <el-select v-model="form.isJudan" placeholder="请选择是否拒单" style="width: 100%">
                    <el-option label="全部" :value="''" />
                    <el-option label="是" :value="true" />
                    <el-option label="否" :value="false" />
                </el-select>
            </el-form-item>
        </el-form>
        <div class="ut-edit-footer">
            <el-button type="primary" @click="query">查询</el-button>
            <el-button @click="reset">重置</el-button>
            <el-button @click="$emit('close')">取 消</el-button>
        </div>
    </div>
</template>

<script>
export default {
    name: 'WorkQuery',
    props: {
        initData: {
            type: Object,
            default: () => ({}),
        }
    },
    data() {
        return {
            rules: {
                // beginDate: [{ required: true, trigger: 'blur', message: '请选择开始日期' }],
                // endDate: [{ required: true, trigger: 'blur', message: '请选择结束日期' }],
            },
            form: {
                beginDate: '',
                endDate: '',
                isJudan: '',
            },
        }
    },
    beforeMount() {
        this.form = Object.assign({}, this.initData)
    },
    methods: {
        query() {
            this.$refs['form'].validate(async (valid) => {
                if (valid) {
                    this.$emit('query', this.form)
                }
            })
        },
        reset() {
            this.form = {
                beginDate: '',
                endDate: '',
                isJudan: '',
            }
        },
    },
}
</script>
