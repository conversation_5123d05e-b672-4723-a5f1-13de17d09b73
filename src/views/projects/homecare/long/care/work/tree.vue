<template>
    <div class="tree-box" :style="{ height: height }">
        <div v-loading="loading">
            <el-input v-model="keWord" placeholder="输入关键字进行查询" style="margin-bottom: 10px" @change="getTreeData">
                <template #append>
                    <div class="search-button" @click="getTreeData">
                        <i class="el-icon-search"></i>
                        搜索
                    </div>
                </template>
            </el-input>
            <ut-tree :tree="treeData" :expanded-keys="expandedKeys" @nodeClick="treeNodeClick">
                <template #icon></template>
            </ut-tree>
        </div>
    </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
    components: {},
    props: {
        showTool: {
            type: Boolean,
            default: false,
        },
        height: {
            type: Number,
            default: 0,
        },
        module: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            type: '',
            treeData: [],
            loading: false,
            treeSelectNode: {},
            dialogFormVisible: false,
            expandedKeys: [],
            keWord: '',
        }
    },
    computed: {
        ...mapGetters({
            comm: 'comm/comm',
        }),
        showEditAndDelete() {
            if (!this.treeSelectNode.id) return false
            if (this.treeSelectNode) {
                return this.treeSelectNode.id !== this.comm.id
            }
            return true
        },
        title() {
            if (this.type === 'add') {
                return '增加分组'
            } else if (this.type === 'edit') return '修改分组'
            return ''
        },
    },
    beforeMount() {
        this.getTreeData()
    },
    created() {},
    methods: {
        async getTreeData() {
            this.loading = true
            let arr = []
            arr.push({ label: this.comm.name, id: this.comm.id, pId: '' })
            const { data } = await this.$ut
                .api('homecarelong/care/scheduling/tree', {
                    communityId: this.comm.id,
                    module: 'long',
                    key: this.keWord,
                })
                .finally(() => {
                    this.loading = false
                })
            if (data) {
                for (const item of data) {
                    arr.push({
                        ...item,
                        label: item.name,
                        id: item.id,
                        pId: item.pId ? item.pId : this.comm.id,
                    })
                }
            }
            this.treeData = arr
            if (this.treeData.length) {
                this.$set(this.treeData[0], 'select', true)
                this.$emit('node-click', { id: '', isRoot: true })
            }
        },
        treeNodeClick(node) {
            console.log(node)
            this.treeSelectNode = node
            let temp = Object.assign({}, node)
            if (node.level === 1) {
                temp.id = ''
                temp.isRoot = true
            } else {
                this.$set(this.treeData[0], 'select', false)
            }
            this.$emit('node-click', temp)
        },
        treeAdd() {
            this.type = 'add'
            this.dialogFormVisible = true
        },
        treeEdit() {
            this.type = 'edit'
            this.dialogFormVisible = true
        },
    },
}
</script>

<style scoped>
:deep(.el-input__inner) {
    padding: 0 5px;
}
.search-button {
    cursor: pointer;
}
</style>
